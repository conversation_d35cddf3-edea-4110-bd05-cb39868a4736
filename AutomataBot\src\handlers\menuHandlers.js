// Menu button handlers
import { getUserSession } from '../utils/sessionManager.js';
import { getUserHistory } from '../config/database.js';
import { formatHistoryMessage } from '../utils/messageFormatter.js';

/**
 * Handle Design FA button
 */
export function handleDesignFA(ctx) {
  const session = getUserSession(ctx.from.id);
  session.waitingFor = 'fa_definition';
  
  const helpText = `📝 **Design Your Finite Automaton**

Please send your automaton definition in this format:

\`\`\`
States: q0,q1,q2
Alphabet: 0,1
Transitions:
q0,0,q1
q0,1,q2
q1,0,q0
q1,1,q2
q2,0,q2
q2,1,q2
Start: q0
Final: q2
\`\`\`

**Tips:**
• Each transition should be on a separate line
• Use comma-separated values
• Make sure all states are defined
• I'll analyze and help optimize your automaton!

Need help? Ask me: "How do I design a DFA that accepts strings ending with '01'?"`;

  ctx.reply(helpText, { parse_mode: 'Markdown' });
}

/**
 * Handle Test Input button
 */
export function handleTestInput(ctx) {
  const session = getUserSession(ctx.from.id);
  if (!session.currentFA) {
    ctx.reply('🚫 Please design or load an automaton first using "🔧 Design FA"');
    return;
  }
  
  session.waitingFor = 'test_input';
  ctx.reply('🧪 **Test Input String**\n\nSend me a string to test against your current automaton.\n\nExample: `0101`', { parse_mode: 'Markdown' });
}

/**
 * Handle Check FA Type button
 */
export function handleCheckFAType(ctx) {
  const session = getUserSession(ctx.from.id);
  session.waitingFor = 'fa_type_check';
  
  ctx.reply('🔍 **Check Automaton Type**\n\nSend me an automaton definition and I\'ll determine if it\'s a DFA or NFA with detailed explanation.', { parse_mode: 'Markdown' });
}

/**
 * Handle NFA to DFA conversion button
 */
export function handleNFAToDFA(ctx) {
  const session = getUserSession(ctx.from.id);
  session.waitingFor = 'nfa_conversion';
  
  ctx.reply('🔄 **Convert NFA to DFA**\n\nSend me an NFA definition and I\'ll convert it to a DFA using subset construction with step-by-step explanation.', { parse_mode: 'Markdown' });
}

/**
 * Handle Minimize DFA button
 */
export function handleMinimizeDFA(ctx) {
  const session = getUserSession(ctx.from.id);
  session.waitingFor = 'dfa_minimization';
  
  ctx.reply('⚡ **Minimize DFA**\n\nSend me a DFA definition and I\'ll minimize it with detailed explanation of the process.', { parse_mode: 'Markdown' });
}

/**
 * Handle AI Help button
 */
export function handleAIHelp(ctx) {
  const helpMessage = `🧠 **AI Assistant Ready!**

I can help you with:

🎯 **Concept Explanations:**
• "Explain DFA minimization"
• "What is the difference between DFA and NFA?"
• "How does subset construction work?"

🔧 **Problem Solving:**
• "Design a DFA that accepts even number of 1s"
• "Convert this NFA to DFA: [your NFA]"
• "Why is my automaton not working?"

📚 **Learning Support:**
• "Give me practice problems"
• "Explain regular languages"
• "Show me examples of finite automata"

Just ask me anything about automata theory in natural language!`;

  ctx.reply(helpMessage, { parse_mode: 'Markdown' });
}

/**
 * Handle Learn Mode button
 */
export function handleLearnMode(ctx) {
  const learningMenu = `📚 **Interactive Learning Mode**

Choose a topic to learn:`;

  ctx.reply(learningMenu, {
    reply_markup: {
      keyboard: [
        [{ text: '📖 DFA Basics' }, { text: '📖 NFA Basics' }],
        [{ text: '📖 Conversions' }, { text: '📖 Minimization' }],
        [{ text: '📖 Regular Languages' }, { text: '📖 Practice Problems' }],
        [{ text: '🔙 Back to Main Menu' }]
      ],
      resize_keyboard: true,
      one_time_keyboard: false
    }
  });
}

/**
 * Handle My History button
 */
export async function handleMyHistory(ctx) {
  try {
    const history = await getUserHistory(ctx.from.id);
    const historyMessage = formatHistoryMessage(history);
    ctx.reply(historyMessage, { parse_mode: 'Markdown' });
  } catch (error) {
    ctx.reply('❌ Error retrieving history.');
  }
}

/**
 * Handle Help button
 */
export function handleHelp(ctx) {
  const helpText = `❓ **Help & Commands**

**Quick Commands:**
• \`/explain [topic]\` - Get AI explanation
• \`/example [type]\` - Get example automata
• \`/simulate [string]\` - Test string (after defining FA)

**Input Format:**
\`\`\`
States: q0,q1,q2
Alphabet: 0,1
Transitions:
q0,0,q1
q0,1,q0
Start: q0
Final: q1
\`\`\`

**Tips:**
• Use the menu buttons for guided operations
• Ask questions in natural language
• I provide step-by-step explanations
• All operations are saved to your history

Need more help? Just ask: "How do I create a DFA?"`;

  ctx.reply(helpText, { parse_mode: 'Markdown' });
}

/**
 * Handle Back to Main Menu button
 */
export function handleBackToMainMenu(ctx) {
  ctx.reply('🏠 Back to main menu', {
    reply_markup: {
      keyboard: [
        [{ text: '🔧 Design FA' }, { text: '🧪 Test Input' }],
        [{ text: '🔍 Check FA Type' }, { text: '🔄 NFA→DFA' }],
        [{ text: '⚡ Minimize DFA' }, { text: '🧠 AI Help' }],
        [{ text: '📚 Learn Mode' }, { text: '📊 My History' }],
        [{ text: '❓ Help' }]
      ],
      resize_keyboard: true,
      one_time_keyboard: false
    }
  });
}
